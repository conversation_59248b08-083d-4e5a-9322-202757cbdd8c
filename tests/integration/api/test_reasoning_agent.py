"""
ReasoningAgent Chat V1 API 集成测试

测试 ReasoningAgent 的 SSE 事件，确保与 V1 Tasks API 返回的 SSE 事件完全一样。
"""

import pytest
import os
from typing import Dict, Any
from .base_api_test_framework import ChatV1APITester

# 设置测试环境
os.environ.setdefault('APP_ENV', 'test')


class ReasoningAgentTester(ChatV1APITester):
    """ReasoningAgent 专用测试器"""
    
    async def test_reasoning_agent_sse_events(self, question: str, save_to_file: bool = True) -> Dict[str, Any]:
        """
        测试 ReasoningAgent 的 SSE 事件（使用通用流式方法），并统计时延信息。
        """
        request_data = {
            "question": question,
            "agent": "ReasoningAgent",
            "user_id": "149789",
        }
        return await self._stream_chat(request_data, save_to_file=save_to_file, file_prefix="reasoning_agent")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_reasoning_agent_sse_events_e2e():
    """
    ReasoningAgent SSE 事件端到端测试（需要服务器运行）
    
    运行此测试前，请确保：
    1. 启动后端服务器：python src/run.py
    2. 服务器运行在 http://localhost:8000
    """
    print("\n" + "="*80)
    print("开始 ReasoningAgent SSE 事件端到端测试")
    print("="*80)
    
    # 初始化测试器
    tester = ReasoningAgentTester()
    
    # 身份认证
    if not await tester.setup_authentication():
        pytest.skip("认证失败，跳过测试")
    
    # 准备测试数据
    test_question = "利用vmcore分析这个NC ************* 在20250724的04:00宕机原因"
    test_question = "利用vmcore分析这个NC *********** 在20250826的宕机原因"
    print(f"📝 测试问题: {test_question}")
    
    # 测试 ReasoningAgent
    print("🚀 开始测试 ReasoningAgent SSE 事件...")
    try:
        result = await tester.test_reasoning_agent_sse_events(test_question, save_to_file=True)
        
        # 验证结果
        tester.validate_sse_response(result)
        
        print("🎉 ReasoningAgent SSE 事件测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_reasoning_agent_capabilities_question():
    """
    测试 ReasoningAgent 回答能力相关问题
    """
    print("\n" + "="*80)
    print("开始 ReasoningAgent 能力问题测试")
    print("="*80)
    
    # 初始化测试器
    tester = ReasoningAgentTester()
    
    # 身份认证
    if not await tester.setup_authentication():
        pytest.skip("认证失败，跳过测试")
    
    # 准备测试数据
    test_question = "你有哪些能力呢"
    print(f"📝 测试问题: {test_question}")
    
    # 测试 ReasoningAgent
    print("🚀 开始测试 ReasoningAgent 能力问题...")
    try:
        result = await tester.test_reasoning_agent_sse_events(test_question, save_to_file=True)
        
        # 验证结果
        tester.validate_sse_response(result)
        
        print("🎉 ReasoningAgent 能力问题测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_reasoning_agent_health_report_query():
    """
    测试 ReasoningAgent 查询健康报告
    """
    print("\n" + "="*80)
    print("开始 ReasoningAgent 健康报告查询测试")
    print("="*80)
    
    # 初始化测试器
    tester = ReasoningAgentTester()
    
    # 身份认证
    if not await tester.setup_authentication():
        pytest.skip("认证失败，跳过测试")
    
    # 准备测试数据
    test_question = "查询用户aliUid 1781574661016173的最近2天健康报告"
    print(f"📝 测试问题: {test_question}")
    
    # 测试 ReasoningAgent
    print("🚀 开始测试 ReasoningAgent 健康报告查询...")
    try:
        result = await tester.test_reasoning_agent_sse_events(test_question, save_to_file=True)
        
        # 验证结果
        tester.validate_sse_response(result)
        
        print("🎉 ReasoningAgent 健康报告查询测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


if __name__ == "__main__":
    """
    直接运行 ReasoningAgent 测试
    
    运行方式：
    python tests/integration/api/test_reasoning_agent.py
    
    # 或使用 pytest
    pytest tests/integration/api/test_reasoning_agent.py -v -s
    
    # 运行特定测试
    pytest tests/integration/api/test_reasoning_agent.py::test_reasoning_agent_sse_events_e2e -v -s
    """
    pytest.main([__file__, "-v", "-s", "--tb=short"])
    print("ReasoningAgent 测试完成！")
