# Chat V1 API 集成测试

这个目录包含了 Chat V1 API 的集成测试，专门测试不同 Agent 的 SSE 事件兼容性。

## 文件结构

```
tests/integration/api/
├── README.md                           # 本文件
├── base_api_test_framework.py          # 基础 API 测试框架
├── test_reasoning_agent.py             # ReasoningAgent 测试
├── test_interactive_agent.py           # InteractiveAgent 测试
├── test_inspect_agent.py               # InspectAgent 测试
├── run_all_agent_tests.py              # 运行所有测试的脚本
└── sse_outputs/                        # SSE 事件输出目录（自动创建）
```

## 测试文件说明

### 1. base_api_test_framework.py
基础测试框架，包含：
- `ChatV1APITester` 类：提供通用的 API 测试功能
- 身份认证方法
- SSE 事件流处理
- 事件格式化和保存
- 性能指标统计

### 2. test_reasoning_agent.py
ReasoningAgent 专用测试，包含：
- 基本 SSE 事件测试
- 能力问题测试
- 健康报告查询测试

### 3. test_interactive_agent.py
InteractiveAgent 专用测试，包含：
- 基本 SSE 事件测试
- 带上下文信息的交互测试
- 故障排查场景测试

### 4. test_inspect_agent.py
InspectAgent 专用测试，包含：
- 基本 SSE 事件测试
- ECS 实例分析测试
- NC IP 分析测试
- 多场景分析测试

## 运行测试

### 前置条件
1. 启动后端服务器：
   ```bash
   python src/run.py
   ```
2. 确保服务器运行在 `http://localhost:8000`

### 运行方式

#### 1. 运行所有测试
```bash
# 使用提供的脚本
python tests/integration/api/run_all_agent_tests.py

# 或使用 pytest
pytest tests/integration/api/ -v -s
```

#### 2. 运行单个 Agent 测试
```bash
# ReasoningAgent 测试
pytest tests/integration/api/test_reasoning_agent.py -v -s

# InteractiveAgent 测试
pytest tests/integration/api/test_interactive_agent.py -v -s

# InspectAgent 测试
pytest tests/integration/api/test_inspect_agent.py -v -s
```

#### 3. 运行特定测试用例
```bash
# 运行 ReasoningAgent 的基本测试
pytest tests/integration/api/test_reasoning_agent.py::test_reasoning_agent_sse_events_e2e -v -s

# 运行 InteractiveAgent 的上下文测试
pytest tests/integration/api/test_interactive_agent.py::test_interactive_agent_with_context -v -s

# 运行 InspectAgent 的 ECS 实例测试
pytest tests/integration/api/test_inspect_agent.py::test_inspect_agent_ecs_instance -v -s
```

#### 4. 直接运行 Python 文件
```bash
# 运行单个测试文件
python tests/integration/api/test_reasoning_agent.py
python tests/integration/api/test_interactive_agent.py
python tests/integration/api/test_inspect_agent.py
```

## 测试输出

### SSE 事件文件
测试运行时会自动保存 SSE 事件到 `sse_outputs/` 目录，文件命名格式：
- `reasoning_agent_YYYYMMDD_HHMMSS.txt`
- `interactive_agent_YYYYMMDD_HHMMSS.txt`
- `inspect_agent_YYYYMMDD_HHMMSS.txt`

### 控制台输出
测试会在控制台输出：
- 实时的 SSE 事件解析
- 性能指标（First token 延迟、总耗时等）
- 事件统计信息
- 测试结果验证

## 测试标记

所有测试都使用了以下 pytest 标记：
- `@pytest.mark.asyncio`：异步测试标记
- `@pytest.mark.integration`：集成测试标记

可以使用标记来筛选测试：
```bash
# 只运行集成测试
pytest -m integration tests/integration/api/ -v -s

# 跳过集成测试
pytest -m "not integration" tests/integration/api/ -v -s
```

## 故障排查

### 常见问题

1. **认证失败**
   - 检查服务器是否正常运行
   - 确认认证接口 `/api/token` 可访问
   - 检查认证凭据（默认：admin/admin）

2. **连接超时**
   - 检查服务器地址和端口
   - 确认防火墙设置
   - 检查网络连接

3. **SSE 事件解析失败**
   - 检查服务器返回的 Content-Type
   - 确认 SSE 事件格式正确
   - 查看详细的错误日志

### 调试技巧

1. **增加详细输出**
   ```bash
   pytest tests/integration/api/ -v -s --tb=long
   ```

2. **只运行失败的测试**
   ```bash
   pytest tests/integration/api/ --lf -v -s
   ```

3. **停在第一个失败的测试**
   ```bash
   pytest tests/integration/api/ -x -v -s
   ```

## 扩展测试

### 添加新的测试用例
1. 在对应的 Agent 测试文件中添加新的测试函数
2. 使用 `@pytest.mark.asyncio` 和 `@pytest.mark.integration` 装饰器
3. 遵循现有的测试模式和命名规范

### 添加新的 Agent 测试
1. 创建新的测试文件：`test_new_agent.py`
2. 继承 `ChatV1APITester` 类
3. 实现 Agent 特定的测试方法
4. 更新 `run_all_agent_tests.py` 脚本

## 注意事项

1. **测试环境**：所有测试都设置了 `APP_ENV=test` 环境变量
2. **超时设置**：HTTP 请求超时设置为 900 秒（15 分钟）
3. **文件保存**：SSE 事件文件会自动保存，注意磁盘空间
4. **并发限制**：建议串行运行测试，避免服务器负载过高
