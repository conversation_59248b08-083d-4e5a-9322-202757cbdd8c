"""
InspectAgent Chat V1 API 集成测试

测试 InspectAgent 的 SSE 事件，确保与 V1 Tasks API 返回的 SSE 事件完全一样。
"""

import pytest
import os
from typing import Dict, Any
from .base_api_test_framework import ChatV1APITester

# 设置测试环境
os.environ.setdefault('APP_ENV', 'test')


class InspectAgentTester(ChatV1APITester):
    """InspectAgent 专用测试器"""
    
    async def test_inspect_agent_sse_events(self, question: str, additional_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """测试 InspectAgent 的 SSE 事件，复用通用流式方法。"""
        request_data = {
            "question": question,
            "agent": "InspectAgent",
            "user_id": "149789",
            "additional_info": additional_info,
        }
        return await self._stream_chat(request_data, save_to_file=True, file_prefix="inspect_agent")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_inspect_agent_sse_events_e2e():
    """
    InspectAgent SSE 事件端到端测试（需要服务器运行）
    
    运行此测试前，请确保：
    1. 启动后端服务器：python src/run.py
    2. 服务器运行在 http://localhost:8000
    """
    print("\n" + "="*80)
    print("开始 InspectAgent SSE 事件端到端测试")
    print("="*80)
    
    # 初始化测试器
    tester = InspectAgentTester()
    
    # 身份认证
    if not await tester.setup_authentication():
        pytest.skip("认证失败，跳过测试")
    
    # 准备测试数据
    test_question = "请分析这段日志，找出可能的异常原因。"
    print(f"📝 测试问题: {test_question}")

    # 测试 InspectAgent
    print("🚀 开始测试 InspectAgent SSE 事件...")
    test_case = {
        "machine_id": "i-bp1fz27ong6p6w693vn5",
        "description": "ECS Instance ID 测试",
        "start_time": "2025-07-28 00:00:00",
        "end_time": "2025-07-30 23:59:59",
    }

    try:
        result = await tester.test_inspect_agent_sse_events(test_question, additional_info=test_case)

        # 验证结果
        tester.validate_sse_response(result)
        
        print("🎉 InspectAgent SSE 事件测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_inspect_agent_ecs_instance():
    """
    测试 InspectAgent 分析 ECS 实例
    """
    print("\n" + "="*80)
    print("开始 InspectAgent ECS 实例分析测试")
    print("="*80)
    
    # 初始化测试器
    tester = InspectAgentTester()
    
    # 身份认证
    if not await tester.setup_authentication():
        pytest.skip("认证失败，跳过测试")
    
    # 准备测试数据
    test_question = "请分析这个ECS实例的运行状态和性能指标"
    additional_info = {
        "machine_id": "i-2zebte5jxlkv2jscbwjb",
        "description": "ECS Instance ID 性能分析",
        "start_time": "2025-07-28 00:00:00",
        "end_time": "2025-07-30 23:59:59",
        "analysis_type": "performance"
    }
    
    print(f"📝 测试问题: {test_question}")
    print(f"📋 附加信息: {additional_info}")
    
    # 测试 InspectAgent
    print("🚀 开始测试 InspectAgent ECS 实例分析...")
    try:
        result = await tester.test_inspect_agent_sse_events(test_question, additional_info)
        
        # 验证结果
        tester.validate_sse_response(result)
        
        print("🎉 InspectAgent ECS 实例分析测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_inspect_agent_nc_ip_analysis():
    """
    测试 InspectAgent 分析 NC IP 地址
    """
    print("\n" + "="*80)
    print("开始 InspectAgent NC IP 分析测试")
    print("="*80)
    
    # 初始化测试器
    tester = InspectAgentTester()
    
    # 身份认证
    if not await tester.setup_authentication():
        pytest.skip("认证失败，跳过测试")
    
    # 准备测试数据
    test_question = "请分析这个NC节点的网络连接和资源使用情况"
    additional_info = {
        "machine_id": "***********",
        "description": "NC IP Address 网络分析",
        "start_time": "2025-07-28 00:00:00",
        "end_time": "2025-07-30 23:59:59",
        "analysis_type": "network"
    }
    
    print(f"📝 测试问题: {test_question}")
    print(f"📋 附加信息: {additional_info}")
    
    # 测试 InspectAgent
    print("🚀 开始测试 InspectAgent NC IP 分析...")
    try:
        result = await tester.test_inspect_agent_sse_events(test_question, additional_info)
        
        # 验证结果
        tester.validate_sse_response(result)
        
        print("🎉 InspectAgent NC IP 分析测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_inspect_agent_multiple_scenarios():
    """
    测试 InspectAgent 多种场景分析
    """
    print("\n" + "="*80)
    print("开始 InspectAgent 多场景分析测试")
    print("="*80)
    
    # 初始化测试器
    tester = InspectAgentTester()
    
    # 身份认证
    if not await tester.setup_authentication():
        pytest.skip("认证失败，跳过测试")
    
    # 测试用例列表
    test_cases = [
        {
            "machine_id": "i-bp1fz27ong6p6w693vn5",
            "description": "ECS Instance ID 测试",
            "start_time": "2025-07-28 00:00:00",
            "end_time": "2025-07-30 23:59:59",
            "scenario": "ECS实例健康检查"
        },
        {
            "machine_id": "***********",
            "description": "NC IP Address 测试",
            "start_time": "2025-07-28 00:00:00",
            "end_time": "2025-07-30 23:59:59",
            "scenario": "NC节点状态检查"
        },
        {
            "machine_id": "i-2zebte5jxlkv2jscbwjb",
            "description": "ECS Instance ID 测试",
            "start_time": "2025-07-28 00:00:00",
            "end_time": "2025-07-30 23:59:59",
            "scenario": "ECS实例性能分析"
        },
    ]
    
    # 只测试第一个场景以避免测试时间过长
    test_case = test_cases[0]
    test_question = f"请分析机器 {test_case['machine_id']} 的运行状态"
    
    print(f"📝 测试问题: {test_question}")
    print(f"📋 测试场景: {test_case['scenario']}")
    
    # 测试 InspectAgent
    print("🚀 开始测试 InspectAgent 多场景分析...")
    try:
        result = await tester.test_inspect_agent_sse_events(test_question, additional_info=test_case)
        
        # 验证结果
        tester.validate_sse_response(result)
        
        print("🎉 InspectAgent 多场景分析测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


if __name__ == "__main__":
    """
    直接运行 InspectAgent 测试
    
    运行方式：
    python tests/integration/api/test_inspect_agent.py
    
    # 或使用 pytest
    pytest tests/integration/api/test_inspect_agent.py -v -s
    
    # 运行特定测试
    pytest tests/integration/api/test_inspect_agent.py::test_inspect_agent_sse_events_e2e -v -s
    """
    pytest.main([__file__, "-v", "-s", "--tb=short"])
    print("InspectAgent 测试完成！")
